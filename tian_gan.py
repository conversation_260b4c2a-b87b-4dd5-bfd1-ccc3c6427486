import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['PingFang HK', 'Hiragino Sans GB', 'STHeiti', 'Arial Unicode MS', 'Hannotate SC', 'DejaVu Sans']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 创建图形和极坐标轴
fig = plt.figure(figsize=(12, 10))
ax = fig.add_subplot(111, projection='polar')

# 天干数据 (10 Heavenly Stems)
tian_gan = ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"]
tian_gan_pinyin = ["jiǎ", "yǐ", "bǐng", "dīng", "wù", "jǐ", "gēng", "xīn", "rén", "guǐ"]
tian_gan_wuxing = ["木", "木", "火", "火", "土", "土", "金", "金", "水", "水"]
tian_gan_colors = ["#7FB80E", "#7FB80E", "#F75000", "#F75000", "#D2B48C", 
                  "#D2B48C", "#D9D9D9", "#D9D9D9", "#006CFF", "#006CFF"]

# 地支数据 (12 Earthly Branches)
di_zhi = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"]
di_zhi_pinyin = ["zǐ", "chǒu", "yín", "mǎo", "chén", "sì", "wǔ", "wèi", "shēn", "yǒu", "xū", "hài"]
zodiac = ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"]
di_zhi_wuxing = ["水", "土", "木", "木", "土", "火", "火", "土", "金", "金", "土", "水"]
di_zhi_colors = ["#006CFF", "#D2B48C", "#7FB80E", "#7FB80E", "#D2B48C", 
                "#F75000", "#F75000", "#D2B48C", "#D9D9D9", "#D9D9D9", "#D2B48C", "#006CFF"]

# 五行数据 (Five Elements)
wu_xing = ["金", "木", "水", "火", "土"]
wu_xing_pinyin = ["jīn", "mù", "shuǐ", "huǒ", "tǔ"]
wu_xing_colors = ["#D9D9D9", "#7FB80E", "#006CFF", "#F75000", "#D2B48C"]

# 设置角度和半径
angles = np.linspace(0, 2*np.pi, 12, endpoint=False)
tian_gan_angles = np.linspace(0, 2*np.pi, 10, endpoint=False)
wu_xing_angles = np.linspace(0, 2*np.pi, 5, endpoint=False)

# 绘制天干环 (外环)
for i, (tg, pinyin, wx, color) in enumerate(zip(tian_gan, tian_gan_pinyin, tian_gan_wuxing, tian_gan_colors)):
    angle = tian_gan_angles[i]
    ax.text(angle, 1.35, tg, ha='center', va='center', fontsize=14, fontweight='bold')
    ax.text(angle, 1.25, pinyin, ha='center', va='center', fontsize=9)
    ax.text(angle, 1.15, wx, ha='center', va='center', fontsize=11, color=color, fontweight='bold')

# 绘制地支生肖环 (中环)
for i, (dz, pinyin, z, wx, color) in enumerate(zip(di_zhi, di_zhi_pinyin, zodiac, di_zhi_wuxing, di_zhi_colors)):
    angle = angles[i]
    ax.text(angle, 1.0, dz, ha='center', va='center', fontsize=14, fontweight='bold')
    ax.text(angle, 0.9, pinyin, ha='center', va='center', fontsize=9)
    ax.text(angle, 0.8, z, ha='center', va='center', fontsize=12)
    ax.text(angle, 0.7, wx, ha='center', va='center', fontsize=11, color=color, fontweight='bold')

# 绘制五行环 (内环)
for i, (wx, pinyin, color) in enumerate(zip(wu_xing, wu_xing_pinyin, wu_xing_colors)):
    angle = wu_xing_angles[i]
    ax.text(angle, 0.5, wx, ha='center', va='center', fontsize=16, color=color, fontweight='bold')
    ax.text(angle, 0.4, pinyin, ha='center', va='center', fontsize=10)

# 绘制分隔线
for angle in angles:
    ax.plot([angle, angle], [0.3, 1.4], color='gray', linestyle='-', alpha=0.3, linewidth=0.5)

# 设置标题
ax.set_title("天干地支生肖五行图\nChinese Zodiac with Five Elements", fontsize=16, pad=20)

# 隐藏极坐标的默认标签和网格
ax.set_xticklabels([])
ax.set_yticklabels([])
ax.set_ylim(0, 1.4)
ax.grid(False)

# 添加图例
legend_elements = [
    plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='#D9D9D9', markersize=10, label='金 Metal'),
    plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='#7FB80E', markersize=10, label='木 Wood'),
    plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='#006CFF', markersize=10, label='水 Water'),
    plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='#F75000', markersize=10, label='火 Fire'),
    plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='#D2B48C', markersize=10, label='土 Earth')
]
ax.legend(handles=legend_elements, loc='center', bbox_to_anchor=(0.5, -0.05), ncol=5)

# 添加中心文字
ax.text(0, 0, "五行\nWu Xing", ha='center', va='center', fontsize=12, 
        bbox=dict(boxstyle="circle,pad=0.3", facecolor="white", alpha=0.8))

plt.tight_layout()
plt.show()

# 创建记忆表
print("\n天干地支记忆表 (Memory Table)")
print("=" * 80)
print("天干 (Heavenly Stems):")
print("-" * 80)
for i, (tg, py, wx) in enumerate(zip(tian_gan, tian_gan_pinyin, tian_gan_wuxing)):
    print(f"{tg}({py}): {wx}", end=" | " if i % 5 != 4 else "\n")

print("\n\n地支 (Earthly Branches):")
print("-" * 80)
for i, (dz, py, z, wx) in enumerate(zip(di_zhi, di_zhi_pinyin, zodiac, di_zhi_wuxing)):
    print(f"{dz}({py}): {z}({wx})", end=" | " if i % 4 != 3 else "\n")

print("\n\n五行 (Five Elements):")
print("-" * 80)
for i, (wx, py) in enumerate(zip(wu_xing, wu_xing_pinyin)):
    print(f"{wx}({py})", end=" | " if i < 4 else "\n")